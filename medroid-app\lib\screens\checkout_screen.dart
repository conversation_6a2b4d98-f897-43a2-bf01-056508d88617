import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import '../models/shopping_cart.dart';
import '../models/order.dart';
import '../services/ecommerce_service.dart';
import '../utils/app_colors.dart';
import '../utils/env_config.dart';
import '../widgets/responsive_centered_container.dart';
import 'order_confirmation_screen.dart';

class CheckoutScreen extends StatefulWidget {
  final List<ShoppingCartItem> cartItems;
  final double subtotal;
  final double tax;
  final double shipping;
  final double total;

  const CheckoutScreen({
    Key? key,
    required this.cartItems,
    required this.subtotal,
    required this.tax,
    required this.shipping,
    required this.total,
  }) : super(key: key);

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final PageController _pageController = PageController();
  int _currentStep = 0;
  bool _isProcessing = false;
  String? _errorMessage;

  // Address form controllers
  final _emailController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _addressLine1Controller = TextEditingController();
  final _addressLine2Controller = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipCodeController = TextEditingController();
  final _countryController = TextEditingController(text: 'US');
  final _phoneController = TextEditingController();

  // Shipping address
  bool _useShippingAddress = false;
  final _shippingFirstNameController = TextEditingController();
  final _shippingLastNameController = TextEditingController();
  final _shippingAddressLine1Controller = TextEditingController();
  final _shippingAddressLine2Controller = TextEditingController();
  final _shippingCityController = TextEditingController();
  final _shippingStateController = TextEditingController();
  final _shippingZipCodeController = TextEditingController();
  final _shippingCountryController = TextEditingController(text: 'US');

  // Payment
  String _selectedShippingMethod = 'standard';
  final Map<String, Map<String, dynamic>> _shippingMethods = {
    'standard': {'name': 'Standard Shipping', 'price': 5.99, 'days': '5-7 business days'},
    'express': {'name': 'Express Shipping', 'price': 12.99, 'days': '2-3 business days'},
    'overnight': {'name': 'Overnight Shipping', 'price': 24.99, 'days': '1 business day'},
  };

  @override
  void dispose() {
    _emailController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _addressLine1Controller.dispose();
    _addressLine2Controller.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _countryController.dispose();
    _phoneController.dispose();
    _shippingFirstNameController.dispose();
    _shippingLastNameController.dispose();
    _shippingAddressLine1Controller.dispose();
    _shippingAddressLine2Controller.dispose();
    _shippingCityController.dispose();
    _shippingStateController.dispose();
    _shippingZipCodeController.dispose();
    _shippingCountryController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < 2) {
      setState(() => _currentStep++);
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep--);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool get _hasPhysicalItems {
    return widget.cartItems.any((item) => item.product.isPhysical);
  }

  double get _shippingCost {
    if (!_hasPhysicalItems) return 0.0;
    return _shippingMethods[_selectedShippingMethod]?['price'] ?? 0.0;
  }

  double get _finalTotal {
    return widget.subtotal + widget.tax + _shippingCost;
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    try {
      // Initialize Stripe if not already done
      Stripe.publishableKey = EnvConfig.stripePublishableKey;

      // Create billing address
      final billingAddress = Address(
        city: _cityController.text,
        country: _countryController.text,
        line1: _addressLine1Controller.text,
        line2: _addressLine2Controller.text.isEmpty ? null : _addressLine2Controller.text,
        postalCode: _zipCodeController.text,
        state: _stateController.text,
      );

      // Create shipping address if different
      Address? shippingAddress;
      if (_useShippingAddress && _hasPhysicalItems) {
        shippingAddress = Address(
          city: _shippingCityController.text,
          country: _shippingCountryController.text,
          line1: _shippingAddressLine1Controller.text,
          line2: _shippingAddressLine2Controller.text.isEmpty ? null : _shippingAddressLine2Controller.text,
          postalCode: _shippingZipCodeController.text,
          state: _shippingStateController.text,
        );
      }

      // Create order data
      final orderData = {
        'items': widget.cartItems.map((item) => {
          'product_id': item.productId,
          'quantity': item.quantity,
          'price': item.unitPrice,
        }).toList(),
        'billing_address': {
          'first_name': _firstNameController.text,
          'last_name': _lastNameController.text,
          'email': _emailController.text,
          'phone': _phoneController.text,
          'address_line_1': _addressLine1Controller.text,
          'address_line_2': _addressLine2Controller.text,
          'city': _cityController.text,
          'state': _stateController.text,
          'zip_code': _zipCodeController.text,
          'country': _countryController.text,
        },
        'shipping_address': _useShippingAddress && _hasPhysicalItems ? {
          'first_name': _shippingFirstNameController.text,
          'last_name': _shippingLastNameController.text,
          'address_line_1': _shippingAddressLine1Controller.text,
          'address_line_2': _shippingAddressLine2Controller.text,
          'city': _shippingCityController.text,
          'state': _shippingStateController.text,
          'zip_code': _shippingZipCodeController.text,
          'country': _shippingCountryController.text,
        } : null,
        'shipping_method': _hasPhysicalItems ? _selectedShippingMethod : null,
        'subtotal': widget.subtotal,
        'tax': widget.tax,
        'shipping': _shippingCost,
        'total': _finalTotal,
      };

      // Process checkout through API
      final result = await EcommerceService.processCheckout(orderData);

      if (result['success']) {
        final clientSecret = result['client_secret'];
        final orderId = result['order_id'];

        // Initialize the payment sheet
        await Stripe.instance.initPaymentSheet(
          paymentSheetParameters: SetupPaymentSheetParameters(
            paymentIntentClientSecret: clientSecret,
            merchantDisplayName: EnvConfig.appName,
            billingDetails: BillingDetails(
              email: _emailController.text,
              name: '${_firstNameController.text} ${_lastNameController.text}',
              phone: _phoneController.text,
              address: billingAddress,
            ),
            shipping: _hasPhysicalItems ? ShippingDetails(
              address: shippingAddress ?? billingAddress,
              name: _useShippingAddress
                  ? '${_shippingFirstNameController.text} ${_shippingLastNameController.text}'
                  : '${_firstNameController.text} ${_lastNameController.text}',
            ) : null,
          ),
        );

        // Present the payment sheet
        await Stripe.instance.presentPaymentSheet();

        // Payment successful, confirm with backend
        final confirmResult = await EcommerceService.confirmPayment(orderId, clientSecret);

        if (confirmResult['success'] && mounted) {
          // Navigate to order confirmation
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => OrderConfirmationScreen(
                order: Order.fromJson(confirmResult['order']),
              ),
            ),
          );
        } else {
          throw Exception(confirmResult['message'] ?? 'Payment confirmation failed');
        }
      } else {
        throw Exception(result['message'] ?? 'Checkout failed');
      }
    } catch (e) {
      String errorMessage = 'Payment failed';
      
      if (e is StripeException) {
        switch (e.error.code) {
          case FailureCode.Canceled:
            errorMessage = 'Payment was cancelled';
            break;
          case FailureCode.Failed:
            errorMessage = 'Payment failed: ${e.error.message}';
            break;
          default:
            errorMessage = 'Payment error: ${e.error.message}';
        }
      } else {
        errorMessage = e.toString();
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }

  Widget _buildBillingAddressForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Billing Information',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          
          TextFormField(
            controller: _emailController,
            decoration: const InputDecoration(
              labelText: 'Email Address *',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Email is required';
              }
              if (!value.contains('@')) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _firstNameController,
                  decoration: const InputDecoration(
                    labelText: 'First Name *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'First name is required';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _lastNameController,
                  decoration: const InputDecoration(
                    labelText: 'Last Name *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Last name is required';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          TextFormField(
            controller: _addressLine1Controller,
            decoration: const InputDecoration(
              labelText: 'Address Line 1 *',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Address is required';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          TextFormField(
            controller: _addressLine2Controller,
            decoration: const InputDecoration(
              labelText: 'Address Line 2',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                flex: 2,
                child: TextFormField(
                  controller: _cityController,
                  decoration: const InputDecoration(
                    labelText: 'City *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'City is required';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _stateController,
                  decoration: const InputDecoration(
                    labelText: 'State *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'State is required';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _zipCodeController,
                  decoration: const InputDecoration(
                    labelText: 'ZIP Code *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'ZIP code is required';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          TextFormField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'Phone Number',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.phone,
          ),
        ],
      ),
    );
  }

  Widget _buildShippingForm() {
    if (!_hasPhysicalItems) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.download, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'Digital Products Only',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Your order contains only digital products.\nNo shipping required.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Shipping Information',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        
        CheckboxListTile(
          title: const Text('Ship to a different address'),
          value: _useShippingAddress,
          onChanged: (value) {
            setState(() => _useShippingAddress = value ?? false);
          },
        ),
        
        if (_useShippingAddress) ...[
          const SizedBox(height: 16),
          // Shipping address form (similar to billing)
          // ... (implement similar to billing form)
        ],
        
        const SizedBox(height: 20),
        const Text(
          'Shipping Method',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        
        ..._shippingMethods.entries.map((entry) {
          return RadioListTile<String>(
            title: Text(entry.value['name']),
            subtitle: Text(
              '\$${entry.value['price'].toStringAsFixed(2)} - ${entry.value['days']}',
            ),
            value: entry.key,
            groupValue: _selectedShippingMethod,
            onChanged: (value) {
              setState(() => _selectedShippingMethod = value!);
            },
          );
        }).toList(),
      ],
    );
  }

  Widget _buildPaymentForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Order Summary',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildSummaryRow('Subtotal', '\$${widget.subtotal.toStringAsFixed(2)}'),
                _buildSummaryRow('Tax', '\$${widget.tax.toStringAsFixed(2)}'),
                _buildSummaryRow('Shipping', _shippingCost > 0 ? '\$${_shippingCost.toStringAsFixed(2)}' : 'Free'),
                const Divider(),
                _buildSummaryRow(
                  'Total',
                  '\$${_finalTotal.toStringAsFixed(2)}',
                  isTotal: true,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 24),
        
        if (_errorMessage != null)
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red[200]!),
            ),
            child: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
        
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _isProcessing ? null : _processPayment,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isProcessing
                ? const CircularProgressIndicator(color: Colors.white)
                : Text(
                    'Pay \$${_finalTotal.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkout'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ResponsiveCenteredContainer(
        child: Column(
          children: [
            // Progress indicator
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  _buildStepIndicator(0, 'Address'),
                  Expanded(child: Container(height: 2, color: Colors.grey[300])),
                  _buildStepIndicator(1, 'Shipping'),
                  Expanded(child: Container(height: 2, color: Colors.grey[300])),
                  _buildStepIndicator(2, 'Payment'),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) => setState(() => _currentStep = index),
                children: [
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: _buildBillingAddressForm(),
                  ),
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: _buildShippingForm(),
                  ),
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: _buildPaymentForm(),
                  ),
                ],
              ),
            ),
            
            // Navigation buttons
            if (_currentStep < 2)
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    if (_currentStep > 0)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _previousStep,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[300],
                            foregroundColor: Colors.black,
                          ),
                          child: const Text('Previous'),
                        ),
                      ),
                    if (_currentStep > 0) const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          if (_currentStep == 0) {
                            if (_formKey.currentState!.validate()) {
                              _nextStep();
                            }
                          } else {
                            _nextStep();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Next'),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label) {
    final isActive = step <= _currentStep;
    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? AppColors.primaryColor : Colors.grey[300],
          ),
          child: Center(
            child: Text(
              '${step + 1}',
              style: TextStyle(
                color: isActive ? Colors.white : Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: isActive ? AppColors.primaryColor : Colors.grey[600],
          ),
        ),
      ],
    );
  }
}