import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/product.dart';
import '../models/shopping_cart.dart';
import '../models/order.dart';
import '../utils/constants.dart';

class EcommerceService {
  static const String baseUrl = Constants.baseUrl;

  // Get authentication headers
  static Map<String, String> _getHeaders({String? token}) {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }
    
    return headers;
  }

  // Products
  static Future<List<Product>> getProducts({
    String? category,
    String? search,
    String? sort,
    String? type,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (category != null) queryParams['category'] = category;
      if (search != null) queryParams['search'] = search;
      if (sort != null) queryParams['sort'] = sort;
      if (type != null) queryParams['type'] = type;
      if (minPrice != null) queryParams['min_price'] = minPrice.toString();
      if (maxPrice != null) queryParams['max_price'] = maxPrice.toString();

      final uri = Uri.parse('$baseUrl/api/shop/products').replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: _getHeaders());

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final productsData = data['products']['data'] ?? data['products'] ?? [];
        return (productsData as List).map((product) => Product.fromJson(product)).toList();
      } else {
        throw Exception('Failed to load products: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading products: $e');
    }
  }

  // Get featured products
  static Future<List<Product>> getFeaturedProducts() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/featured-products'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final productsData = data['products'] ?? [];
        return (productsData as List).map((product) => Product.fromJson(product)).toList();
      } else {
        throw Exception('Failed to load featured products: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading featured products: $e');
    }
  }

  // Get provider's products
  static Future<List<Product>> getProviderProducts({String? token}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/provider/products'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final productsData = data['products'] ?? [];
        return (productsData as List).map((product) => Product.fromJson(product)).toList();
      } else {
        throw Exception('Failed to load provider products: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading provider products: $e');
    }
  }

  static Future<List<ProductCategory>> getCategories() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/categories'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final categoriesData = data['categories'] ?? [];
        return (categoriesData as List).map((category) => ProductCategory.fromJson(category)).toList();
      } else {
        throw Exception('Failed to load categories: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading categories: $e');
    }
  }

  static Future<Product> getProduct(String slug) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/products/$slug'),
        headers: _getHeaders(),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Product.fromJson(data['product']);
      } else {
        throw Exception('Failed to load product: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading product: $e');
    }
  }

  // Shopping Cart
  static Future<List<ShoppingCartItem>> getCartItems({String? token}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/cart'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final cartData = data['cart_items'] ?? [];
        return (cartData as List).map((item) => ShoppingCartItem.fromJson(item)).toList();
      } else {
        throw Exception('Failed to load cart: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading cart: $e');
    }
  }

  static Future<void> addToCart(String productId, int quantity, {String? token}) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/shop/cart/add'),
        headers: _getHeaders(token: token),
        body: json.encode({
          'product_id': productId,
          'quantity': quantity,
        }),
      );

      if (response.statusCode != 200) {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Failed to add to cart');
      }
    } catch (e) {
      throw Exception('Error adding to cart: $e');
    }
  }

  static Future<void> updateCartItem(String productId, int quantity, {String? token}) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/shop/cart/$productId'),
        headers: _getHeaders(token: token),
        body: json.encode({
          'quantity': quantity,
        }),
      );

      if (response.statusCode != 200) {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Failed to update cart');
      }
    } catch (e) {
      throw Exception('Error updating cart: $e');
    }
  }

  static Future<void> removeFromCart(String productId, {String? token}) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/shop/cart/$productId'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode != 200) {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Failed to remove from cart');
      }
    } catch (e) {
      throw Exception('Error removing from cart: $e');
    }
  }

  static Future<void> clearCart({String? token}) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/shop/cart'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode != 200) {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Failed to clear cart');
      }
    } catch (e) {
      throw Exception('Error clearing cart: $e');
    }
  }

  // Orders
  static Future<List<Order>> getUserOrders({String? token}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/orders'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final ordersData = data['orders'] ?? [];
        return (ordersData as List).map((order) => Order.fromJson(order)).toList();
      } else {
        throw Exception('Failed to load orders: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading orders: $e');
    }
  }

  static Future<Order> getOrderDetails(String orderNumber, {String? token}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/shop/orders/$orderNumber'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Order.fromJson(data['order']);
      } else {
        throw Exception('Failed to load order details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error loading order details: $e');
    }
  }

  static Future<Map<String, dynamic>> processCheckout(Map<String, dynamic> orderData, {String? token}) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/shop/checkout'),
        headers: _getHeaders(token: token),
        body: json.encode(orderData),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Checkout failed');
      }
    } catch (e) {
      throw Exception('Error processing checkout: $e');
    }
  }

  static Future<Map<String, dynamic>> confirmPayment(String orderId, String clientSecret, {String? token}) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/shop/orders/$orderId/confirm-payment'),
        headers: _getHeaders(token: token),
        body: json.encode({
          'payment_intent_id': clientSecret,
        }),
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Payment confirmation failed');
      }
    } catch (e) {
      throw Exception('Error confirming payment: $e');
    }
  }

  static Future<void> cancelOrder(String orderNumber, {String? token}) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/shop/orders/$orderNumber/cancel'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode != 200) {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Failed to cancel order');
      }
    } catch (e) {
      throw Exception('Error cancelling order: $e');
    }
  }

  // Product Management (for providers)
  static Future<void> createProduct(Map<String, dynamic> productData, {List<dynamic>? images, String? token}) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/api/provider/products'),
      );

      // Add headers (remove Content-Type for multipart)
      final headers = _getHeaders(token: token);
      headers.remove('Content-Type');
      request.headers.addAll(headers);

      // Add product data
      productData.forEach((key, value) {
        if (value != null) {
          request.fields[key] = value.toString();
        }
      });

      // Add images
      if (images != null) {
        for (int i = 0; i < images.length; i++) {
          final image = images[i];
          if (image is File) {
            request.files.add(
              await http.MultipartFile.fromPath(
                'images[]',
                image.path,
              ),
            );
          }
        }
      }

      final response = await request.send();
      if (response.statusCode != 201) {
        final responseBody = await response.stream.bytesToString();
        final data = json.decode(responseBody);
        throw Exception(data['message'] ?? 'Failed to create product');
      }
    } catch (e) {
      throw Exception('Error creating product: $e');
    }
  }

  static Future<void> updateProduct(String productId, Map<String, dynamic> productData, {List<dynamic>? images, String? token}) async {
    try {
      var request = http.MultipartRequest(
        'PUT',
        Uri.parse('$baseUrl/api/provider/products/$productId'),
      );

      // Add headers (remove Content-Type for multipart)
      final headers = _getHeaders(token: token);
      headers.remove('Content-Type');
      request.headers.addAll(headers);

      // Add product data
      productData.forEach((key, value) {
        if (value != null) {
          request.fields[key] = value.toString();
        }
      });

      // Add new images
      if (images != null) {
        for (int i = 0; i < images.length; i++) {
          final image = images[i];
          if (image is File) {
            request.files.add(
              await http.MultipartFile.fromPath(
                'images[]',
                image.path,
              ),
            );
          }
        }
      }

      final response = await request.send();
      if (response.statusCode != 200) {
        final responseBody = await response.stream.bytesToString();
        final data = json.decode(responseBody);
        throw Exception(data['message'] ?? 'Failed to update product');
      }
    } catch (e) {
      throw Exception('Error updating product: $e');
    }
  }

  static Future<void> deleteProduct(String productId, {String? token}) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/provider/products/$productId'),
        headers: _getHeaders(token: token),
      );

      if (response.statusCode != 200) {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Failed to delete product');
      }
    } catch (e) {
      throw Exception('Error deleting product: $e');
    }
  }

  static Future<void> toggleProductStatus(String productId, bool isActive, {String? token}) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/provider/products/$productId/status'),
        headers: _getHeaders(token: token),
        body: json.encode({
          'is_active': isActive,
        }),
      );

      if (response.statusCode != 200) {
        final data = json.decode(response.body);
        throw Exception(data['message'] ?? 'Failed to update product status');
      }
    } catch (e) {
      throw Exception('Error updating product status: $e');
    }
  }
}