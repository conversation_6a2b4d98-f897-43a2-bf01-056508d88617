import 'package:flutter/material.dart';

/// App color constants based on the new design system
class AppColors {
  // New color palette
  static const Color tealSurge = Color(0xFF17C3B2);
  static const Color mintGlow = Color(0xFF8BE9C8);
  static const Color midnightNavy = Color(0xFF032B3E);
  static const Color cloudWhite = Color(0xFFFBFCFB);
  static const Color coralPop = Color(0xFFFF9F6E);
  static const Color slateGrey = Color(0xFF6E7A8A);

  // Primary color (for backward compatibility)
  static const Color primaryColor = tealSurge;

  // Semantic colors
  static const Color error = Color(0xFFED4337);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFFC107);
  static const Color info = Color(0xFF2196F3);

  // Background colors
  static const Color backgroundLight =
      Color(0xFFF3F9F7); // Light teal background (from Discover screen)
  static const Color backgroundDark = midnightNavy;
  static const Color surfaceLight = Colors.white;
  static const Color surfaceDark = Color(0xFF0A3D53);

  // Text colors
  static const Color textPrimaryLight = midnightNavy;
  static const Color textSecondaryLight = slateGrey;
  static const Color textPrimaryDark = Colors.white;
  static const Color textSecondaryDark = Color(0xFFB0B7C3);

  // Gradient definitions
  static const List<Color> primaryGradient = [
    tealSurge,
    mintGlow,
  ];

  static const List<Color> secondaryGradient = [
    midnightNavy,
    Color(0xFF0A4D6A),
  ];

  static const List<Color> accentGradient = [
    coralPop,
    Color(0xFFFFBB91),
  ];

  // Helper methods for gradients
  static LinearGradient getPrimaryGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: primaryGradient,
      begin: begin,
      end: end,
    );
  }

  static LinearGradient getSecondaryGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: secondaryGradient,
      begin: begin,
      end: end,
    );
  }

  static LinearGradient getAccentGradient({
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      colors: accentGradient,
      begin: begin,
      end: end,
    );
  }
}
